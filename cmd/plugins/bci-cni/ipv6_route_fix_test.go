/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"net"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/vishvananda/netlink"

	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink/mock"
)

func TestAddIPv6GatewayNeighbor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockNetlink := mock.NewMockInterface(ctrl)
	plugin := &bciCniPlugin{
		nlink: mockNetlink,
	}

	ctx := context.Background()
	
	// 创建测试用的网络接口
	testLink := &netlink.Veth{
		LinkAttrs: netlink.LinkAttrs{
			Index: 2,
			Name:  "test-veth",
		},
	}

	tests := []struct {
		name        string
		gateway     net.IP
		expectCall  bool
		expectError bool
		description string
	}{
		{
			name:        "IPv4 gateway should return error",
			gateway:     net.ParseIP("***********"),
			expectCall:  false,
			expectError: true,
			description: "IPv4地址应该返回错误",
		},
		{
			name:        "Link-local IPv6 gateway should skip neighbor",
			gateway:     net.ParseIP("fe80::1"),
			expectCall:  false,
			expectError: false,
			description: "链路本地IPv6地址应该跳过邻居条目添加",
		},
		{
			name:        "Global IPv6 gateway should add neighbor",
			gateway:     net.ParseIP("240c:4085:4:3101::1"),
			expectCall:  true,
			expectError: false,
			description: "全局IPv6地址应该添加邻居条目",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectCall {
				mockNetlink.EXPECT().NeighAdd(gomock.Any()).Return(nil).Times(1)
			}

			err := plugin.addIPv6GatewayNeighbor(ctx, testLink, tt.gateway)

			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

func TestIPv6RouteConfiguration(t *testing.T) {
	// 测试IPv6路由配置的不同场景
	tests := []struct {
		name        string
		gateway     string
		expectType  string
		description string
	}{
		{
			name:        "Link-local gateway",
			gateway:     "fe80::1",
			expectType:  "link-local",
			description: "链路本地网关应该直接添加默认路由",
		},
		{
			name:        "Global unicast gateway",
			gateway:     "240c:4085:4:3101::1",
			expectType:  "global",
			description: "全局单播网关应该先添加网关路由",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gateway := net.ParseIP(tt.gateway)
			assert.NotNil(t, gateway, "网关地址解析失败")

			if tt.expectType == "link-local" {
				assert.True(t, gateway.IsLinkLocalUnicast(), tt.description)
			} else {
				assert.False(t, gateway.IsLinkLocalUnicast(), tt.description)
				assert.True(t, gateway.IsGlobalUnicast(), tt.description)
			}
		})
	}
}

// 测试路由配置的正确性
func TestRouteConfigurationCorrectness(t *testing.T) {
	// 测试网关路由配置
	gateway := net.ParseIP("240c:4085:4:3101::1")
	
	// 正确的网关路由配置
	gatewayRoute := netlink.Route{
		LinkIndex: 2,
		Dst: &net.IPNet{
			IP:   gateway,
			Mask: net.CIDRMask(128, 128),
		},
		Scope:    netlink.SCOPE_LINK,
		Priority: 1000,
		// 注意：不应该设置Gw字段，因为这本身就是到网关的路由
		// 注意：不应该设置Src字段，避免内核拒绝
	}

	assert.Equal(t, 2, gatewayRoute.LinkIndex)
	assert.Equal(t, gateway, gatewayRoute.Dst.IP)
	assert.Equal(t, net.CIDRMask(128, 128), gatewayRoute.Dst.Mask)
	assert.Equal(t, netlink.SCOPE_LINK, gatewayRoute.Scope)
	assert.Nil(t, gatewayRoute.Gw, "网关路由不应该设置Gw字段")

	// 正确的默认路由配置
	defaultRoute := netlink.Route{
		LinkIndex: 2,
		Dst: &net.IPNet{
			IP:   net.IPv6zero,
			Mask: net.CIDRMask(0, 128),
		},
		Scope:    netlink.SCOPE_UNIVERSE,
		Gw:       gateway,
		Priority: 1000,
	}

	assert.Equal(t, 2, defaultRoute.LinkIndex)
	assert.Equal(t, net.IPv6zero, defaultRoute.Dst.IP)
	assert.Equal(t, net.CIDRMask(0, 128), defaultRoute.Dst.Mask)
	assert.Equal(t, netlink.SCOPE_UNIVERSE, defaultRoute.Scope)
	assert.Equal(t, gateway, defaultRoute.Gw)
}
